import { nanoid } from 'nanoid';
import {
  AgentConfig,
  AgentMessage,
  ToolCall,
  ToolResult,
  ExecutionContext,
  AgentInstance,
  ProjectType
} from '@/types';
import { providerManager } from '@/providers';
import { toolRegistry } from '@/tools';
import { sessionManager } from '@/session/manager';
import { projectDiscovery } from '@/context/discovery';
import { logger } from '@/utils/logger';


export class Agent implements AgentInstance {
  public readonly id: string;
  public readonly config: AgentConfig;
  private context: ExecutionContext;
  private isProcessing: boolean = false;

  constructor(agentConfig: AgentConfig, workingDirectory?: string) {
    this.id = nanoid();
    this.config = agentConfig;
    
    // Initialize context - will be properly set when session is created/loaded
    this.context = {
      sessionId: '',
      workingDirectory: workingDirectory ?? process.cwd(),
      environment: process.env as Record<string, string>,
      projectContext: {
        root: workingDirectory ?? process.cwd(),
        type: 'unknown',
        structure: {
          directories: [],
          files: [],
          totalFiles: 0,
          totalDirectories: 0,
          lastIndexed: new Date(),
        },
        dependencies: [],
        configuration: {},
      },
      agent: this,
    };

    logger.info(`Agent created`, { 
      agentId: this.id, 
      provider: agentConfig.provider,
      model: agentConfig.model 
    }, 'Agent');
  }

  public async initialize(sessionId?: string): Promise<void> {
    try {
      let session;
      
      if (sessionId) {
        // Load existing session
        session = await sessionManager.loadSession(sessionId);
        logger.info(`Loaded existing session`, { sessionId }, 'Agent', this.id);
      } else {
        // Create new session
        session = await sessionManager.createSession(
          undefined, 
          this.context.workingDirectory
        );
        logger.info(`Created new session`, { sessionId: session.id }, 'Agent', this.id);
      }

      // Update context with session information
      this.context = {
        sessionId: session.id,
        workingDirectory: session.workingDirectory,
        environment: session.context.environment,
        projectContext: {
          root: session.workingDirectory,
          type: (session.metadata['projectType'] as ProjectType) || 'unknown',
          structure: session.context.projectStructure,
          dependencies: session.context.dependencies,
          configuration: {},
          gitInfo: session.context.gitInfo,
        },
        agent: this,
      };

      logger.info(`Agent initialized successfully`, { 
        agentId: this.id,
        sessionId: session.id,
        projectType: session.metadata['projectType']
      }, 'Agent', this.id);
    } catch (error) {
      logger.error(`Failed to initialize agent`, error, 'Agent', this.id);
      throw error;
    }
  }

  public async sendMessage(message: string): Promise<string> {
    if (this.isProcessing) {
      throw new Error('Agent is currently processing another request');
    }

    this.isProcessing = true;
    
    try {
      logger.info(`Processing message`, { 
        messageLength: message.length 
      }, 'Agent', this.context.sessionId);

      // Add user message to session
      const userMessage: AgentMessage = {
        role: 'user',
        content: message,
      };
      await sessionManager.addMessage(userMessage);

      // Get conversation history
      const messages = sessionManager.getMessages();

      // Get LLM provider
      const provider = providerManager.getProvider(this.config.provider);
      if (!provider) {
        throw new Error(`Provider not found: ${this.config.provider}`);
      }

      // Validate provider configuration
      if (!provider.validateConfig(this.config)) {
        throw new Error(`Invalid configuration for provider: ${this.config.provider}`);
      }

      // Get available tools
      const tools = toolRegistry.getAllTools();

      // Send message to LLM with tools
      const response = await provider.sendToolMessage(messages, tools, this.config);

      // Process tool calls if any
      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolResults = await this.executeTools(response.toolCalls);
        
        // Add assistant message with tool calls
        const assistantMessage: AgentMessage = {
          role: 'assistant',
          content: response.message,
          toolCalls: response.toolCalls,
        };
        await sessionManager.addMessage(assistantMessage);

        // Add tool results as messages
        for (const result of toolResults) {
          const toolMessage: AgentMessage = {
            role: 'tool',
            content: JSON.stringify(result.result),
            toolCallId: result.toolCallId,
          };
          await sessionManager.addMessage(toolMessage);
        }

        // Get final response from LLM after tool execution
        const finalMessages = sessionManager.getMessages();
        const finalResponse = await provider.sendMessage(finalMessages, this.config);
        
        // Add final assistant message
        const finalAssistantMessage: AgentMessage = {
          role: 'assistant',
          content: finalResponse,
        };
        await sessionManager.addMessage(finalAssistantMessage);

        logger.info(`Message processed with tools`, { 
          toolCallsCount: response.toolCalls.length,
          responseLength: finalResponse.length 
        }, 'Agent', this.context.sessionId);

        return finalResponse;
      } else {
        // No tool calls, just add the response
        const assistantMessage: AgentMessage = {
          role: 'assistant',
          content: response.message,
        };
        await sessionManager.addMessage(assistantMessage);

        logger.info(`Message processed without tools`, { 
          responseLength: response.message.length 
        }, 'Agent', this.context.sessionId);

        return response.message;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to process message`, error, 'Agent', this.context.sessionId);
      throw new Error(`Failed to process message: ${errorMessage}`);
    } finally {
      this.isProcessing = false;
    }
  }

  public async executeTools(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];

    logger.info(`Executing ${toolCalls.length} tool calls`, undefined, 'Agent', this.context.sessionId);

    // Check if we should execute tools in parallel
    const shouldExecuteInParallel = toolCalls.length > 1 && this.canExecuteInParallel(toolCalls);

    if (shouldExecuteInParallel) {
      return this.executeToolsInParallel(toolCalls);
    }

    // Sequential execution for dependent or unsafe tools
    for (const toolCall of toolCalls) {
      const result = await this.executeSingleTool(toolCall);
      results.push(result);
    }

    logger.info(`Tool execution completed`, {
      totalTools: toolCalls.length,
      successful: results.filter(r => !r.error).length,
      failed: results.filter(r => r.error).length
    }, 'Agent', this.context.sessionId);

    return results;
  }

  private async executeSingleTool(toolCall: ToolCall): Promise<ToolResult> {
    try {
      const tool = toolRegistry.getTool(toolCall.function.name);
      if (!tool) {
        return {
          toolCallId: toolCall.id,
          result: {
            success: false,
            error: `Tool not found: ${toolCall.function.name}`
          },
          error: 'TOOL_NOT_FOUND',
        };
      }

      // Parse tool arguments
      let args;
      try {
        args = JSON.parse(toolCall.function.arguments);
      } catch (error) {
        return {
          toolCallId: toolCall.id,
          result: {
            success: false,
            error: `Invalid tool arguments: ${toolCall.function.arguments}`
          },
          error: 'INVALID_ARGUMENTS',
        };
      }

      // Validate arguments against tool schema
      const validationResult = tool.parameters.safeParse(args);
      if (!validationResult.success) {
        return {
          toolCallId: toolCall.id,
          result: {
            success: false,
            error: `Invalid arguments for tool ${tool.name}`,
            metadata: { details: validationResult.error.issues }
          },
          error: 'VALIDATION_ERROR',
        };
      }

      logger.debug(`Executing tool: ${tool.name}`, { args }, 'Agent', this.context.sessionId);

      // Execute the tool
      const startTime = Date.now();
      const result = await tool.execute(validationResult.data, this.context);
      const duration = Date.now() - startTime;

      logger.debug(`Tool executed: ${tool.name}`, {
        duration,
        success: result.success
      }, 'Agent', this.context.sessionId);

      return {
        toolCallId: toolCall.id,
        result,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Tool execution failed: ${toolCall.function.name}`, error, 'Agent', this.context.sessionId);

      return {
        toolCallId: toolCall.id,
        result: {
          success: false,
          error: `Tool execution failed: ${errorMessage}`,
          metadata: { toolName: toolCall.function.name }
        },
        error: 'EXECUTION_ERROR',
      };
    }
  }

  private canExecuteInParallel(toolCalls: ToolCall[]): boolean {
    // Tools that should not be executed in parallel
    const sequentialTools = [
      'write_file', 'delete_file', 'move_file', 'copy_file',
      'execute_command', 'execute_script', 'create_directory'
    ];

    // Check if any tool calls contain sequential tools
    return !toolCalls.some(call => sequentialTools.includes(call.function.name));
  }

  private async executeToolsInParallel(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    logger.info(`Executing ${toolCalls.length} tools in parallel`, undefined, 'Agent', this.context.sessionId);

    const promises = toolCalls.map(toolCall => this.executeSingleTool(toolCall));
    const results = await Promise.all(promises);

    logger.info(`Parallel tool execution completed`, {
      totalTools: toolCalls.length,
      successful: results.filter(r => !r.error).length,
      failed: results.filter(r => r.error).length
    }, 'Agent', this.context.sessionId);

    return results;
  }

  public getContext(): ExecutionContext {
    return { ...this.context };
  }

  public async updateWorkingDirectory(newPath: string): Promise<void> {
    try {
      logger.info(`Updating working directory`, { 
        from: this.context.workingDirectory,
        to: newPath 
      }, 'Agent', this.context.sessionId);

      // Discover new project context
      const projectContext = await projectDiscovery.discoverProject(newPath);
      
      // Update context
      this.context.workingDirectory = newPath;
      this.context.projectContext = projectContext;

      // Update session if available
      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        currentSession.workingDirectory = newPath;
        await sessionManager.refreshSessionContext(currentSession);
      }

      logger.info(`Working directory updated successfully`, { 
        newPath,
        projectType: projectContext.type 
      }, 'Agent', this.context.sessionId);
    } catch (error) {
      logger.error(`Failed to update working directory`, error, 'Agent', this.context.sessionId);
      throw error;
    }
  }

  public async refreshContext(): Promise<void> {
    try {
      logger.info(`Refreshing agent context`, undefined, 'Agent', this.context.sessionId);

      const projectContext = await projectDiscovery.refreshProject(this.context.workingDirectory);
      this.context.projectContext = projectContext;

      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        await sessionManager.refreshSessionContext(currentSession);
      }

      logger.info(`Agent context refreshed`, { 
        files: projectContext.structure.totalFiles 
      }, 'Agent', this.context.sessionId);
    } catch (error) {
      logger.error(`Failed to refresh context`, error, 'Agent', this.context.sessionId);
      throw error;
    }
  }

  public getSessionId(): string {
    return this.context.sessionId;
  }

  public isReady(): boolean {
    return !!this.context.sessionId && !this.isProcessing;
  }

  public async cleanup(): Promise<void> {
    try {
      logger.info(`Cleaning up agent`, undefined, 'Agent', this.context.sessionId);

      // Save current session
      const currentSession = sessionManager.getCurrentSession();
      if (currentSession) {
        await sessionManager.saveSession(currentSession);
      }

      // Stop file watching
      projectDiscovery.stopWatching(this.context.workingDirectory);

      logger.info(`Agent cleanup completed`, undefined, 'Agent', this.context.sessionId);
    } catch (error) {
      logger.error(`Failed to cleanup agent`, error, 'Agent', this.context.sessionId);
    }
  }
}

export { Agent as AgentCore };
