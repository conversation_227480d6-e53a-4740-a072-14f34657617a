#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { Agent } from '@/agent/core';
import { sessionManager } from '@/session/manager';
import { config } from '@/config';
import { logger, LogLevel } from '@/utils/logger';
import { CommandOptions, AgentConfig } from '@/types';

// Helper function to safely get parent options
function getParentOptions(cmd: Command): CommandOptions {
  const parent = cmd.parent as Command | undefined;
  return parent?.opts() as CommandOptions || {};
}

// Helper function to safely get grandparent options (for nested commands)
function getGrandparentOptions(cmd: Command): CommandOptions {
  const parent = cmd.parent as Command | undefined;
  const grandparent = parent?.parent as Command | undefined;
  return grandparent?.opts() as CommandOptions || {};
}

const program = new Command();

program
  .name('agentic')
  .description('AI-powered CLI tool with agentic capabilities')
  .version('1.0.0');

// Global options
program
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-d, --debug', 'Enable debug logging')
  .option('--provider <provider>', 'LLM provider (deepseek|ollama)', 'deepseek')
  .option('--model <model>', 'Model to use')
  .option('--session <sessionId>', 'Session ID to use')
  .option('--temperature <temp>', 'Temperature for LLM (0.0-1.0)', parseFloat)
  .option('--max-tokens <tokens>', 'Maximum tokens for LLM response', parseInt);

// Chat command - main interactive mode
program
  .command('chat')
  .description('Start interactive chat with the AI agent')
  .option('-m, --message <message>', 'Send a single message instead of interactive mode')
  .action(async (chatOptions, cmd) => {
    const globalOpts = getParentOptions(cmd);
    await runChatCommand(chatOptions, globalOpts);
  });

// Session management commands
const sessionCmd = program
  .command('session')
  .description('Manage sessions');

sessionCmd
  .command('list')
  .description('List all sessions')
  .action(async (_options, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await listSessions(globalOpts);
  });

sessionCmd
  .command('create')
  .description('Create a new session')
  .option('-n, --name <name>', 'Session name')
  .option('-d, --directory <dir>', 'Working directory')
  .action(async (options, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await createSession(options, globalOpts);
  });

sessionCmd
  .command('delete <sessionId>')
  .description('Delete a session')
  .action(async (sessionId, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await deleteSession(sessionId, globalOpts);
  });

sessionCmd
  .command('export <sessionId> <outputPath>')
  .description('Export a session to file')
  .action(async (sessionId, outputPath, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await exportSession(sessionId, outputPath, globalOpts);
  });

sessionCmd
  .command('import <importPath>')
  .description('Import a session from file')
  .action(async (importPath, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await importSession(importPath, globalOpts);
  });

sessionCmd
  .command('stats [sessionId]')
  .description('Show session statistics')
  .action(async (sessionId, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await showSessionStats(sessionId, globalOpts);
  });

sessionCmd
  .command('optimize [sessionId]')
  .description('Optimize session by removing old messages')
  .action(async (sessionId, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await optimizeSession(sessionId, globalOpts);
  });

sessionCmd
  .command('backup [sessionId]')
  .description('Create a backup of the session')
  .action(async (sessionId, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await backupSession(sessionId, globalOpts);
  });

sessionCmd
  .command('cleanup')
  .description('Clean up old sessions')
  .option('--max-age <days>', 'Maximum age in days (default: 30)', parseInt)
  .action(async (options, cmd) => {
    const globalOpts = getGrandparentOptions(cmd);
    await cleanupSessions(options, globalOpts);
  });

// Configuration commands
const configCmd = program
  .command('config')
  .description('Manage configuration');

configCmd
  .command('show')
  .description('Show current configuration')
  .action(async () => {
    console.log(JSON.stringify(config.getConfig(), null, 2));
  });

configCmd
  .command('set-api-key <provider> <apiKey>')
  .description('Set API key for a provider')
  .action(async (provider, apiKey) => {
    if (provider === 'deepseek') {
      config.setProviderApiKey('deepseek', apiKey);
      console.log(chalk.green('✓ Deepseek API key set successfully'));
    } else {
      console.log(chalk.red('✗ Unsupported provider. Use: deepseek'));
    }
  });

configCmd
  .command('reset')
  .description('Reset configuration to defaults')
  .action(async () => {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Are you sure you want to reset configuration to defaults?',
        default: false,
      },
    ]);

    if (confirm) {
      config.resetConfig();
      console.log(chalk.green('✓ Configuration reset to defaults'));
    }
  });

// Tools command - for testing and debugging tools
const toolsCmd = program
  .command('tools')
  .description('Tool management and testing');

toolsCmd
  .command('list')
  .description('List all available tools')
  .action(async () => {
    const { toolRegistry } = await import('./tools');
    const tools = toolRegistry.getAllTools();

    console.log(chalk.blue(`Available tools (${tools.length}):`));
    console.log();

    for (const tool of tools) {
      console.log(chalk.green(`🔧 ${tool.name}`));
      console.log(chalk.gray(`   ${tool.description}`));
      console.log();
    }
  });

toolsCmd
  .command('test <toolName>')
  .description('Test a specific tool with sample parameters')
  .action(async (toolName) => {
    try {
      const { toolRegistry } = await import('./tools');
      const tool = toolRegistry.getTool(toolName);

      if (!tool) {
        console.log(chalk.red(`✗ Tool not found: ${toolName}`));
        return;
      }

      console.log(chalk.blue(`Testing tool: ${tool.name}`));
      console.log(chalk.gray(`Description: ${tool.description}`));
      console.log();

      // Create a minimal test context
      const testContext = {
        sessionId: 'test-session',
        workingDirectory: process.cwd(),
        environment: process.env as Record<string, string>,
        projectContext: {
          root: process.cwd(),
          type: 'nodejs' as any,
          structure: { directories: [], files: [], totalFiles: 0, totalDirectories: 0, lastIndexed: new Date() },
          dependencies: [],
          configuration: {},
        },
        agent: {} as any,
      };

      // Test with empty parameters (tools should handle this gracefully)
      const result = await tool.execute({}, testContext);

      console.log(chalk.blue('Test Result:'));
      console.log(JSON.stringify(result, null, 2));
    } catch (error: any) {
      console.error(chalk.red('✗ Tool test failed:'), error.message);
    }
  });

// Project analysis command
program
  .command('analyze')
  .description('Analyze the current project')
  .option('-d, --directory <dir>', 'Directory to analyze', process.cwd())
  .option('--include-files', 'Include file content analysis')
  .action(async (options, cmd) => {
    const globalOpts = getParentOptions(cmd);
    await analyzeProject(options, globalOpts);
  });

// Project analysis function
async function analyzeProject(options: { directory?: string; includeFiles?: boolean }, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Analyzing project...').start();

    const { projectDiscovery } = await import('./context/discovery');
    const projectContext = await projectDiscovery.discoverProject(options.directory ?? process.cwd());

    spinner.stop();

    console.log(chalk.blue('📊 Project Analysis Results:'));
    console.log();

    console.log(chalk.green('📁 Project Structure:'));
    console.log(chalk.gray(`   Type: ${projectContext.type}`));
    console.log(chalk.gray(`   Root: ${projectContext.root}`));
    console.log(chalk.gray(`   Files: ${projectContext.structure.totalFiles}`));
    console.log(chalk.gray(`   Directories: ${projectContext.structure.totalDirectories}`));
    console.log();

    if (projectContext.dependencies.length > 0) {
      console.log(chalk.green('📦 Dependencies:'));
      projectContext.dependencies.slice(0, 10).forEach(dep => {
        console.log(chalk.gray(`   ${dep.name}@${dep.version} (${dep.type})`));
      });
      if (projectContext.dependencies.length > 10) {
        console.log(chalk.gray(`   ... and ${projectContext.dependencies.length - 10} more`));
      }
      console.log();
    }

    if (Object.keys(projectContext.configuration).length > 0) {
      console.log(chalk.green('⚙️  Configuration Files:'));
      Object.keys(projectContext.configuration).forEach(file => {
        console.log(chalk.gray(`   ${file}`));
      });
      console.log();
    }

    if (options.includeFiles && projectContext.structure.files.length > 0) {
      console.log(chalk.green('📄 Sample Files:'));
      projectContext.structure.files.slice(0, 5).forEach(file => {
        console.log(chalk.gray(`   ${file.path} (${file.size} bytes)`));
      });
      console.log();
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    console.error(chalk.red('✗ Error analyzing project:'), errorMessage);
    if (globalOpts.debug && errorStack) {
      console.error(errorStack);
    }
    process.exit(1);
  }
}

// Main chat command implementation
async function runChatCommand(options: { message?: string }, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);
    
    const agentConfig = await buildAgentConfig(globalOpts);
    const agent = new Agent(agentConfig, process.cwd());
    
    // Initialize agent with session
    await agent.initialize(globalOpts.session);
    
    console.log(chalk.blue('🤖 Agentic CLI Agent initialized'));
    console.log(chalk.gray(`Provider: ${agentConfig.provider} | Model: ${agentConfig.model}`));
    console.log(chalk.gray(`Session: ${agent.getSessionId()}`));
    console.log(chalk.gray(`Working Directory: ${agent.getContext().workingDirectory}`));
    console.log();

    if (options.message) {
      // Single message mode
      await processSingleMessage(agent, options.message);
    } else {
      // Interactive mode
      await runInteractiveChat(agent);
    }

    await agent.cleanup();
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    console.error(chalk.red('✗ Error:'), errorMessage);
    if (globalOpts.debug && errorStack) {
      console.error(errorStack);
    }
    process.exit(1);
  }
}

async function processSingleMessage(agent: Agent, message: string): Promise<void> {
  const spinner = ora('Processing message...').start();
  
  try {
    const response = await agent.sendMessage(message);
    spinner.stop();
    
    console.log(chalk.green('🤖 Agent:'));
    console.log(response);
  } catch (error: unknown) {
    spinner.fail('Failed to process message');
    throw error;
  }
}

async function runInteractiveChat(agent: Agent): Promise<void> {
  console.log(chalk.yellow('💬 Interactive chat mode. Type "exit" to quit, "help" for commands.'));
  console.log();

  while (true) {
    try {
      const { message } = await inquirer.prompt([
        {
          type: 'input',
          name: 'message',
          message: chalk.blue('You:'),
          validate: (input) => input.trim().length > 0 || 'Please enter a message',
        },
      ]);

      if (message.toLowerCase() === 'exit') {
        console.log(chalk.yellow('👋 Goodbye!'));
        break;
      }

      if (message.toLowerCase() === 'help') {
        showChatHelp();
        continue;
      }

      if (message.toLowerCase().startsWith('/')) {
        await handleChatCommand(agent, message);
        continue;
      }

      const spinner = ora('Agent is thinking...').start();
      
      try {
        const response = await agent.sendMessage(message);
        spinner.stop();
        
        console.log();
        console.log(chalk.green('🤖 Agent:'));
        console.log(response);
        console.log();
      } catch (error: unknown) {
        spinner.fail('Failed to process message');
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(chalk.red('Error:'), errorMessage);
        console.log();
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name === 'ExitPromptError') {
        console.log(chalk.yellow('\n👋 Goodbye!'));
        break;
      }
      throw error;
    }
  }
}

function showChatHelp(): void {
  console.log();
  console.log(chalk.yellow('Available commands:'));
  console.log(chalk.gray('  exit          - Exit the chat'));
  console.log(chalk.gray('  help          - Show this help'));
  console.log(chalk.gray('  /status       - Show agent status'));
  console.log(chalk.gray('  /context      - Show project context'));
  console.log(chalk.gray('  /refresh      - Refresh project context'));
  console.log(chalk.gray('  /cd <path>    - Change working directory'));
  console.log(chalk.gray('  /session      - Show session info'));
  console.log();
}

async function handleChatCommand(agent: Agent, command: string): Promise<void> {
  const parts = command.slice(1).split(' ');
  const cmd = parts[0]?.toLowerCase() || '';
  const args = parts.slice(1);

  try {
    switch (cmd) {
      case 'status':
        console.log(chalk.blue('Agent Status:'));
        console.log(`  Ready: ${agent.isReady()}`);
        console.log(`  Session: ${agent.getSessionId()}`);
        console.log(`  Provider: ${agent.config.provider}`);
        console.log(`  Model: ${agent.config.model}`);
        break;

      case 'context': {
        const context = agent.getContext();
        console.log(chalk.blue('Project Context:'));
        console.log(`  Working Directory: ${context.workingDirectory}`);
        console.log(`  Project Type: ${context.projectContext.type}`);
        console.log(`  Files: ${context.projectContext.structure.totalFiles}`);
        console.log(`  Directories: ${context.projectContext.structure.totalDirectories}`);
        break;
      }

      case 'refresh':
        const spinner = ora('Refreshing project context...').start();
        await agent.refreshContext();
        spinner.succeed('Project context refreshed');
        break;

      case 'cd': {
        if (args.length === 0) {
          console.log(chalk.red('Usage: /cd <path>'));
          break;
        }
        const newPath = args.join(' ');
        const cdSpinner = ora(`Changing directory to ${newPath}...`).start();
        await agent.updateWorkingDirectory(newPath);
        cdSpinner.succeed(`Changed directory to ${newPath}`);
        break;
      }

      case 'session':
        console.log(chalk.blue('Session Info:'));
        console.log(`  ID: ${agent.getSessionId()}`);
        console.log(`  Working Directory: ${agent.getContext().workingDirectory}`);
        break;

      default:
        console.log(chalk.red(`Unknown command: ${cmd}`));
        showChatHelp();
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(chalk.red('Command error:'), errorMessage);
  }
  console.log();
}

// Session management functions
async function listSessions(globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const sessions = await sessionManager.listSessions();

    if (sessions.length === 0) {
      console.log(chalk.yellow('No sessions found.'));
      return;
    }

    console.log(chalk.blue(`Found ${sessions.length} sessions:`));
    console.log();

    for (const session of sessions) {
      console.log(chalk.green(`📁 ${session.name}`));
      console.log(chalk.gray(`   ID: ${session.id}`));
      console.log(chalk.gray(`   Created: ${session.createdAt.toLocaleString()}`));
      console.log(chalk.gray(`   Updated: ${session.updatedAt.toLocaleString()}`));
      console.log(chalk.gray(`   Directory: ${session.workingDirectory}`));
      console.log(chalk.gray(`   Messages: ${session.messages.length}`));
      console.log();
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(chalk.red('✗ Error listing sessions:'), errorMessage);
    process.exit(1);
  }
}

async function createSession(options: any, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Creating new session...').start();

    const session = await sessionManager.createSession(
      options.name,
      options.directory
    );

    spinner.succeed('Session created successfully');

    console.log(chalk.green(`✓ Session created:`));
    console.log(chalk.gray(`   ID: ${session.id}`));
    console.log(chalk.gray(`   Name: ${session.name}`));
    console.log(chalk.gray(`   Directory: ${session.workingDirectory}`));
  } catch (error: any) {
    console.error(chalk.red('✗ Error creating session:'), error.message);
    process.exit(1);
  }
}

async function deleteSession(sessionId: string, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to delete session ${sessionId}?`,
        default: false,
      },
    ]);

    if (!confirm) {
      console.log(chalk.yellow('Session deletion cancelled.'));
      return;
    }

    const spinner = ora('Deleting session...').start();

    await sessionManager.deleteSession(sessionId);

    spinner.succeed('Session deleted successfully');
    console.log(chalk.green(`✓ Session ${sessionId} deleted`));
  } catch (error: any) {
    console.error(chalk.red('✗ Error deleting session:'), error.message);
    process.exit(1);
  }
}

async function exportSession(sessionId: string, outputPath: string, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Exporting session...').start();

    await sessionManager.exportSession(sessionId, outputPath);

    spinner.succeed('Session exported successfully');
    console.log(chalk.green(`✓ Session exported to: ${outputPath}`));
  } catch (error: any) {
    console.error(chalk.red('✗ Error exporting session:'), error.message);
    process.exit(1);
  }
}

async function importSession(importPath: string, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Importing session...').start();

    const session = await sessionManager.importSession(importPath);

    spinner.succeed('Session imported successfully');
    console.log(chalk.green(`✓ Session imported:`));
    console.log(chalk.gray(`   ID: ${session.id}`));
    console.log(chalk.gray(`   Name: ${session.name}`));
  } catch (error: any) {
    console.error(chalk.red('✗ Error importing session:'), error.message);
    process.exit(1);
  }
}

async function showSessionStats(sessionId: string | undefined, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Gathering session statistics...').start();

    const stats = await sessionManager.getSessionStats(sessionId);

    spinner.stop();

    console.log(chalk.blue('📊 Session Statistics:'));
    console.log();

    console.log(chalk.green('📁 Session Info:'));
    console.log(chalk.gray(`   ID: ${stats.session.id}`));
    console.log(chalk.gray(`   Name: ${stats.session.name}`));
    console.log(chalk.gray(`   Age: ${Math.round(stats.session.age / 1000 / 60)} minutes`));
    console.log(chalk.gray(`   Last Activity: ${Math.round(stats.session.lastActivity / 1000 / 60)} minutes ago`));
    console.log(chalk.gray(`   Working Directory: ${stats.session.workingDirectory}`));
    console.log();

    console.log(chalk.green('💬 Messages:'));
    console.log(chalk.gray(`   Total: ${stats.messages.total}`));
    Object.entries(stats.messages.byRole).forEach(([role, count]) => {
      console.log(chalk.gray(`   ${role}: ${count}`));
    });
    console.log(chalk.gray(`   With Tool Calls: ${stats.messages.withToolCalls}`));
    console.log();

    console.log(chalk.green('📂 Context:'));
    console.log(chalk.gray(`   Files: ${stats.context.files}`));
    console.log(chalk.gray(`   Directories: ${stats.context.directories}`));
    console.log(chalk.gray(`   Dependencies: ${stats.context.dependencies}`));
    console.log(chalk.gray(`   Last Indexed: ${stats.context.lastIndexed.toLocaleString()}`));
    console.log();

    if (Object.keys(stats.metadata).length > 0) {
      console.log(chalk.green('🏷️  Metadata:'));
      Object.entries(stats.metadata).forEach(([key, value]) => {
        console.log(chalk.gray(`   ${key}: ${value}`));
      });
    }
  } catch (error: any) {
    console.error(chalk.red('✗ Error getting session stats:'), error.message);
    process.exit(1);
  }
}

async function optimizeSession(sessionId: string | undefined, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Optimizing session...').start();

    await sessionManager.optimizeSession(sessionId);

    spinner.succeed('Session optimized successfully');
    console.log(chalk.green('✓ Session optimized - old messages removed'));
  } catch (error: any) {
    console.error(chalk.red('✗ Error optimizing session:'), error.message);
    process.exit(1);
  }
}

async function backupSession(sessionId: string | undefined, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const spinner = ora('Creating session backup...').start();

    const backupPath = await sessionManager.backupSession(sessionId);

    spinner.succeed('Session backup created successfully');
    console.log(chalk.green(`✓ Session backed up to: ${backupPath}`));
  } catch (error: any) {
    console.error(chalk.red('✗ Error backing up session:'), error.message);
    process.exit(1);
  }
}

async function cleanupSessions(options: any, globalOpts: CommandOptions): Promise<void> {
  try {
    setupLogging(globalOpts);

    const maxAge = (options.maxAge || 30) * 24 * 60 * 60 * 1000; // Convert days to milliseconds

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to clean up sessions older than ${options.maxAge || 30} days?`,
        default: false,
      },
    ]);

    if (!confirm) {
      console.log(chalk.yellow('Session cleanup cancelled.'));
      return;
    }

    const spinner = ora('Cleaning up old sessions...').start();

    await sessionManager.cleanupOldSessions(maxAge);

    spinner.succeed('Session cleanup completed');
    console.log(chalk.green('✓ Old sessions cleaned up'));
  } catch (error: any) {
    console.error(chalk.red('✗ Error cleaning up sessions:'), error.message);
    process.exit(1);
  }
}

// Utility functions
function setupLogging(options: CommandOptions): void {
  if (options.debug) {
    logger.setLogLevel(LogLevel.DEBUG);
  } else if (options.verbose) {
    logger.setLogLevel(LogLevel.INFO);
  }
}

async function buildAgentConfig(options: CommandOptions): Promise<AgentConfig> {
  const cliConfig = config.getConfig();
  const provider = options.provider || cliConfig.defaultProvider;

  if (!['deepseek', 'ollama'].includes(provider)) {
    throw new Error(`Unsupported provider: ${provider}. Use: deepseek, ollama`);
  }

  const providerConfig = config.getProviderConfig(provider);

  let model = options.model;
  if (!model) {
    model = provider === 'deepseek'
      ? providerConfig.defaultModel
      : providerConfig.defaultModel;
  }

  const agentConfig: AgentConfig = {
    provider,
    model,
    ...(options.temperature !== undefined && { temperature: options.temperature }),
    ...(options.maxTokens !== undefined && { maxTokens: options.maxTokens }),
  };

  if (provider === 'deepseek') {
    const deepseekConfig = providerConfig as any;
    if (deepseekConfig.apiKey) {
      agentConfig.apiKey = deepseekConfig.apiKey;
    }
    if (providerConfig.baseUrl) {
      agentConfig.baseUrl = providerConfig.baseUrl;
    }

    if (!agentConfig.apiKey) {
      throw new Error('Deepseek API key not configured. Use: agentic config set-api-key deepseek <key>');
    }
  } else if (provider === 'ollama') {
    if (providerConfig.baseUrl) {
      agentConfig.baseUrl = providerConfig.baseUrl;
    }
  }

  return agentConfig;
}

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', String(reason));
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n👋 Shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n👋 Shutting down gracefully...'));
  process.exit(0);
});

// Parse command line arguments
program.parse();
