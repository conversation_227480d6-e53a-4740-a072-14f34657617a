import { z } from 'zod';

// Core Agent Types
export interface AgentConfig {
  provider: 'deepseek' | 'ollama';
  model: string;
  temperature?: number;
  maxTokens?: number;
  apiKey?: string;
  baseUrl?: string;
}

export interface AgentMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  result: ToolExecutionResult;
  error?: string;
}

export interface ToolExecutionResult {
  success: boolean;
  data?: unknown;
  error?: string;
  message?: string;
  metadata?: Record<string, unknown>;
}

// Tool System Types
export interface Tool {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  execute: (params: unknown, context: ExecutionContext) => Promise<ToolExecutionResult>;
}

export interface ExecutionContext {
  sessionId: string;
  workingDirectory: string;
  environment: Record<string, string>;
  projectContext: ProjectContext;
  agent: AgentInstance;
}

export interface AgentInstance {
  id: string;
  config: AgentConfig;
  sendMessage: (message: string) => Promise<string>;
  executeTools: (toolCalls: ToolCall[]) => Promise<ToolResult[]>;
}

// Session Management Types
export interface Session {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  workingDirectory: string;
  context: SessionContext;
  messages: AgentMessage[];
  metadata: Record<string, unknown>;
}

export interface SessionContext {
  projectStructure: ProjectStructure;
  fileIndex: FileIndex;
  dependencies: DependencyInfo[];
  environment: Record<string, string>;
  gitInfo?: GitInfo | undefined;
}

// Project Context Types
export interface ProjectContext {
  root: string;
  type: ProjectType;
  structure: ProjectStructure;
  dependencies: DependencyInfo[];
  configuration: ProjectConfiguration;
  gitInfo?: GitInfo | undefined;
}

export interface ProjectStructure {
  directories: DirectoryNode[];
  files: FileNode[];
  totalFiles: number;
  totalDirectories: number;
  lastIndexed: Date;
}

export interface DirectoryNode {
  path: string;
  name: string;
  children: (DirectoryNode | FileNode)[];
  permissions: FilePermissions;
}

export interface FileNode {
  path: string;
  name: string;
  size: number;
  extension: string;
  mimeType: string;
  lastModified: Date;
  permissions: FilePermissions;
  content?: string;
  summary?: string;
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
  mode: string;
}

export interface FileIndex {
  files: Map<string, FileNode>;
  directories: Map<string, DirectoryNode>;
  byExtension: Map<string, FileNode[]>;
  bySize: Map<string, FileNode[]>;
  searchIndex: Map<string, FileNode[]>;
}

// Project Types
export type ProjectType = 
  | 'nodejs' 
  | 'python' 
  | 'rust' 
  | 'go' 
  | 'java' 
  | 'csharp' 
  | 'cpp' 
  | 'web' 
  | 'mobile' 
  | 'unknown';

export interface DependencyInfo {
  name: string;
  version: string;
  type: 'production' | 'development' | 'peer' | 'optional';
  source: string;
  description?: string;
}

export interface ProjectConfiguration {
  packageManager?: string;
  buildTool?: string;
  testFramework?: string;
  linter?: string;
  formatter?: string;
  bundler?: string;
  framework?: string;
  language?: string;
  version?: string;
}

export interface GitInfo {
  branch: string;
  commit: string;
  remote?: string;
  status: GitStatus;
  isRepo: boolean;
}

export interface GitStatus {
  staged: string[];
  unstaged: string[];
  untracked: string[];
  ahead: number;
  behind: number;
}

// LLM Provider Types
export interface LLMProvider {
  name: string;
  sendMessage: (messages: AgentMessage[], config: AgentConfig) => Promise<string>;
  sendToolMessage: (messages: AgentMessage[], tools: Tool[], config: AgentConfig) => Promise<{
    message: string;
    toolCalls?: ToolCall[];
  }>;
  validateConfig: (config: AgentConfig) => boolean;
}

// Configuration Types
export interface CLIConfig {
  defaultProvider: 'deepseek' | 'ollama';
  providers: {
    deepseek: {
      apiKey?: string | undefined;
      baseUrl: string;
      defaultModel: string;
    };
    ollama: {
      baseUrl: string;
      defaultModel: string;
    };
  };
  session: {
    autoSave: boolean;
    maxHistory: number;
    persistContext: boolean;
  };
  context: {
    autoIndex: boolean;
    watchFiles: boolean;
    maxFileSize: number;
    excludePatterns: string[];
  };
  tools: {
    allowShellExecution: boolean;
    allowFileOperations: boolean;
    allowNetworkAccess: boolean;
    restrictedPaths: string[];
    maxConcurrentExecutions: number;
    timeoutMs: number;
    allowDangerousCommands: boolean;
    allowedCommands: string[];
    blockedCommands: string[];
    enableRealTimeOutput: boolean;
    maxParallelTools: number;
  };
}

// Command Types
export interface CommandOptions {
  provider?: 'deepseek' | 'ollama';
  model?: string;
  session?: string;
  verbose?: boolean;
  debug?: boolean;
  temperature?: number;
  maxTokens?: number;
}

// Error Types
export class AgentError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

export class ToolError extends Error {
  constructor(
    message: string,
    public toolName: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'ToolError';
  }
}

export class SessionError extends Error {
  constructor(
    message: string,
    public sessionId?: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'SessionError';
  }
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
